<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\AddressOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\RadioOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\ArrayOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'AXASIMPLYPR',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates/products',
        'filename' => 'axa-simply-protection-v1.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'product-form',
        'title' => 'AXA Simply Protection',
        'version' => '1.0.0',
        'description' => "Scheda Adesione AXA Simply Protection",
        'processor' => PdfProcessor::class,
        'signers' => ["contractor"],    
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ],
        ],

        //
        // This is not stored in the database anymore.
        //
        'overlayArray' => [
            //new UserOverlay(1, 40, 42, ['properties' => ['name', 'lastname'],]),
            //new TextOverlay(1, 144, 42, [], 'ciao ciao'),
            new ArrayOverlay(1, 169, 53.5, [
                'key' => 'moduloAdesione'
            ]),
            new ArrayOverlay(1, 110, 68.5, [
                'key' => 'capitaleAssicurato',
                'fmtCurrency' => true
            ]),
            new ArrayOverlay(1, 110, 76, [
                'key' => 'indennitaMensile',
                'fmtCurrency' => true
            ]),
            new ArrayOverlay(1, 44, 96, [
                'key' => 'datiAssicurato.localita'
            ]),
            new SubjectOverlay(1, 50, 92, [
                'role' => 'contractor',
                'properties' => ['name', 'lastname'],
                'separator' => ' ',
            ]),
            new SubjectOverlay(1, 130, 92, [
                'role' => 'contractor',
                'properties' => ['taxCode'],
            ]),
            new SubjectOverlay(1, 45, 97, [
                'role' => 'contractor',
                'properties' => ['birthdate'],
            ]),
            new SubjectOverlay(1, 135, 96.5, [
                'role' => 'contractor',
                'properties' => ['birthplaceCity'],
            ]),
            new SubjectOverlay(1, 35, 119.5, [
                'role' => 'contractor',
                'properties' => ['phone'],
            ]),
            new SubjectOverlay(1, 117, 119.5, [
                'role' => 'contractor',
                'properties' => ['email'],
            ]),
            new RadioOverlay(1, 0, 0, [
                'key' => 'pacchetto',
                'options' => [
                    '1' => new TextOverlay(1, 21.3, 159.3, [], "x"),
                    '2' => new TextOverlay(1, 21.3, 179, [], "x"),
                    '3' => new TextOverlay(1, 21.3, 196.5, [], "x"),
                    '4' => new TextOverlay(1, 21.3, 218.2, [], "x"),
                ],
            ]),
            new RadioOverlay(1, 0, 0, [
                'key' => 'durata',
                'options' => [
                    '5' => new TextOverlay(1, 21.3, 259.1, [], "x"),
                    '10' => new TextOverlay(1, 40.8, 259.1, [], "x"),
                ],
            ]),
            new ArrayOverlay(2, 97, 34.8, [
                'key' => 'premioUnico',
                'fmtCurrency' => true
            ]),
            new RadioOverlay(2, 0, 0, [
                'key' => 'modalitaPagamento',
                'options' => [
                    '1' => new TextOverlay(2, 21.3, 59, [], "x"),
                    '2' => new TextOverlay(2, 21.3, 62.8, [], "x"),
                    '3' => new TextOverlay(2, 21.3, 67.2, [], "x"),
                ],
            ]),
            new RadioOverlay(3, 0, 0, [
                'key' => 'healthData.salute1',
                'options' => [
                    '0' => new TextOverlay(3, 170, 75, [], "X"),
                    '1' => new TextOverlay(3, 170, 75, [], "X")
                ],
            ]),

            /*new SubjectOverlay(1, 37, 60, [
                'role' => 'contractor',
                'properties' => ['lastname'],
            ]),
            new SubjectOverlay(1, 120, 60, [
                'role' => 'contractor',
                'properties' => ['name'],
            ]),
            new SubjectOverlay(1, 38.5, 64.5, [
                'role' => 'contractor',
                'method' => 'getPrintableBirthdate',
            ]),
            new TextOverlay(1, 110, 64.5, [], "@todo"),*/

            
        ],
    ],

    // @FIXME! per prodotti direct creare direttamente in fase di install
    // un product-policy come copia esatta del product-form, perché sono di fatto
    // la stessa cosa.
    'policy' => [
        'title' => 'AXA Simply Protection - Proposta di adesione',
        'signers' => ["contractor"], 
        'description' => "Proposta assicurativa da firmare.",
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ]
        ],
    ],

    //
    // This defines the form data to be collected for this product and THIS document.
    // Defines the validation rules.
    //
    'formRules' =>[
        
    ],
];